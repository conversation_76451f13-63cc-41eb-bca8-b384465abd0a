import { httpsCallable } from 'firebase/functions';
import { useEffect, useState } from 'react';

import { AppCloudFunctions, type UserEntity } from '@/constants/core.constants';
import { firebaseFunctions } from '@/root-context';

export interface WithdrawalStatus {
  currentWithdrawn: number;
  remainingLimit: number;
  maxLimit: number;
  resetAt: string;
}

export interface UseWithdrawalStatusResult {
  withdrawalStatus: WithdrawalStatus | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useWithdrawalStatus(
  currentUser: UserEntity | null,
): UseWithdrawalStatusResult {
  const [withdrawalStatus, setWithdrawalStatus] =
    useState<WithdrawalStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWithdrawalStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const getWithdrawalLimitStatusFunction = httpsCallable<
        {},
        WithdrawalStatus
      >(firebaseFunctions, AppCloudFunctions.getWithdrawalLimitStatus);

      const result = await getWithdrawalLimitStatusFunction({});
      setWithdrawalStatus(result.data);
    } catch (err) {
      console.error('Error fetching withdrawal status:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to fetch withdrawal status',
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchWithdrawalStatus();
    }
  }, [currentUser]);

  return {
    withdrawalStatus,
    loading,
    error,
    refetch: fetchWithdrawalStatus,
  };
}
